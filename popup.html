<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BookSmart</title>
    <style>
        :root {
            /* Light theme colors */
            --bg-primary: #f9f9f9;
            --text-primary: #333;
            --text-secondary: #555;
            --gradient-primary: linear-gradient(45deg, #007BFF, #00BFFF);
            --gradient-success: linear-gradient(45deg, #28a745, #20c997);
            --shadow-light: rgba(0,0,0,0.1);
            --shadow-medium: rgba(0,0,0,0.15);
        }

        [data-theme="dark"] {
            /* Dark theme colors */
            --bg-primary: #2d2d2d;
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --gradient-primary: linear-gradient(45deg, #4dabf7, #74c0fc);
            --gradient-success: linear-gradient(45deg, #51cf66, #69db7c);
            --shadow-light: rgba(0,0,0,0.3);
            --shadow-medium: rgba(0,0,0,0.4);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            width: 280px;
            padding: 16px;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            text-align: center;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        h1 {
            font-size: 18px;
            color: var(--text-primary);
            margin-top: 0;
            margin-bottom: 16px;
        }
        button {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            font-weight: 500;
            color: #fff;
            background: var(--gradient-primary);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            box-shadow: 0 2px 4px var(--shadow-light);
            margin-bottom: 8px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-medium);
        }
        button:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px var(--shadow-light);
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        #view-bookmarks-btn {
            background: var(--gradient-success);
        }
        #status {
            margin-top: 16px;
            font-size: 14px;
            color: var(--text-secondary);
            min-height: 40px;
            line-height: 1.4;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>BookSmart</h1>
    <button id="bookmark-all-btn">Bookmark All Tabs</button>
    <button id="view-bookmarks-btn">View Bookmarks</button>
    <div id="status"></div>
    <script src="js/popup.js"></script>
</body>
</html>
