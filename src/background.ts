/**
 * background.ts
 *
 * This is the core service worker for the extension. It handles the main logic:
 * 1.  Receives messages from the popup.
 * 2.  Reads all open tabs.
 * 3.  Gathers metadata for each tab.
 * 4.  Uses WebLLM to classify and group tabs.
 * 5.  Saves the grouped bookmarks to chrome.storage.local.
 */

import * as webllm from "@mlc-ai/web-llm";

// Define the structure for our smart bookmark objects
interface SmartBookmark {
    url: string;
    title: string;
    description: string;
    lastAccessed: number;
    bookmarkedAt: number;
    visitCount: number; // Total visit count from Chrome history
    notes: string;
    favIconUrl?: string;
}

let engine: webllm.MLCEngineInterface | null = null;

// Listen for the message from the popup script
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    if (request.action === "bookmarkAllTabs") {
        processAndSaveTabs().then((result) => {
            sendResponse(result);
        }).catch(error => {
            console.error("Error processing tabs:", error);
            sendResponse({ status: "Error", message: error.message });
        });
        return true; // Indicates we will send a response asynchronously
    } else if (request.action === "getAllBookmarks") {
        getAllBookmarks().then((bookmarks) => {
            sendResponse({ status: "Success", bookmarks: bookmarks });
        }).catch(error => {
            console.error("Error retrieving bookmarks:", error);
            sendResponse({ status: "Error", message: error.message });
        });
        return true; // Indicates we will send a response asynchronously
    } else if (request.action === "openInNewTab") {
        chrome.tabs.create({ url: request.url }).then(() => {
            sendResponse({ status: "Tab opened" });
        }).catch(error => {
            console.error("Error opening tab:", error);
            sendResponse({ status: "Error", message: error.message });
        });
        return true; // Indicates we will send a response asynchronously
    } else if (request.action === "resetAllBookmarks") {
        resetAllBookmarks().then((result) => {
            sendResponse(result);
        }).catch(error => {
            console.error("Error resetting bookmarks:", error);
            sendResponse({ status: "Error", message: error.message });
        });
        return true; // Indicates we will send a response asynchronously
    }
});



async function initializeEngine() {
    if (engine) {
        console.log("Using existing WebLLM engine");
        return engine;
    }

    console.log("Initializing WebLLM engine...");

    try {
        // Use a better model for more accurate classification
        // Options (in order of quality vs speed):
        // "Llama-3.1-8B-Instruct-q4f32_1-MLC" - Best quality (6GB VRAM)
        // "Qwen2.5-7B-Instruct-q4f32_1-MLC" - Great for classification (5.9GB VRAM)
        // "Llama-3.2-3B-Instruct-q4f32_1-MLC" - Good balance (2.9GB VRAM)
        // "Llama-3.2-1B-Instruct-q4f32_1-MLC" - Current (fast but inaccurate)
        const selectedModel = "Llama-3.2-3B-Instruct-q4f32_1-MLC";

        engine = new webllm.MLCEngine();

        // Initialize with progress callback and timeout
        console.log("Loading model:", selectedModel);
        await engine.reload(selectedModel, {
            temperature: 0.1,
            top_p: 0.9,
        });

        console.log("WebLLM engine initialized successfully");
        return engine;
    } catch (error) {
        console.error("Failed to initialize WebLLM engine:", error);
        throw new Error(`WebLLM initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

async function processAndSaveTabs() {
    console.log("Starting tab processing...");

    try {
        // 1. Get all tabs from the current window, excluding system pages
        const allTabs = await chrome.tabs.query({ currentWindow: true });
        console.log(`Found ${allTabs.length} total tabs`);

        const tabs = allTabs.filter(tab => {
            if (!tab.url) return false;

            // Skip chrome internal pages and extension pages
            if (tab.url.startsWith('chrome://') ||
                tab.url.startsWith('chrome-extension://') ||
                tab.url.startsWith('edge://') ||
                tab.url.startsWith('about:')) {
                return false;
            }

            return tab.url.startsWith('http://') || tab.url.startsWith('https://');
        });

        console.log(`Filtered to ${tabs.length} valid tabs`);

    // 2. Get existing bookmarks to check for duplicates
    let existingUrls: Set<string>;

    try {
        console.log("Starting deduplication check...");
        existingUrls = await getExistingUrlsSet();
        console.log('Existing urls >>>>');
        console.log(existingUrls);
        console.log(`Found ${existingUrls.size} existing bookmarks for deduplication`);
    } catch (error) {
        console.error("Error getting existing URLs, proceeding without deduplication:", error);
        existingUrls = new Set<string>(); // Empty set if there's an error
    }

    // Initialize WebLLM engine
    console.log("Initializing WebLLM engine...");
    const llmEngine = await initializeEngine();
    console.log("WebLLM engine initialized successfully");

    const bookmarksToGroup: SmartBookmark[] = [];
    let duplicateCount = 0;

    for (const tab of tabs) {
        // URL is mandatory, skip if missing
        if (!tab.url) continue;

        // Check if this URL is already bookmarked
        if (existingUrls.has(tab.url)) {
            duplicateCount++;
            console.log(`Skipping duplicate URL: ${tab.url}`);
            continue;
        }

        // Get additional info for each tab (optional)
        const description = tab.id ? await getMetaDescription(tab.id) : '';
        const visitCount = await getVisitCount(tab.url);

        const bookmark: SmartBookmark = {
            url: tab.url,
            title: tab.title || extractDomainName(tab.url), // Fallback to domain name
            description: description,
            lastAccessed: tab.lastAccessed || Date.now(),
            bookmarkedAt: Date.now(),
            visitCount: visitCount,
            notes: "",
            favIconUrl: tab.favIconUrl || ''
        };

        bookmarksToGroup.push(bookmark);
    }

    if (bookmarksToGroup.length === 0) {
        const totalTabs = tabs.length;
        if (duplicateCount > 0) {
            console.log(`No new bookmarks to save. Found ${duplicateCount} duplicate URLs out of ${totalTabs} total tabs.`);
            return {
                status: "No new bookmarks",
                message: `All ${totalTabs} tabs were already bookmarked. Skipped ${duplicateCount} duplicates.`,
                newBookmarks: 0,
                duplicates: duplicateCount,
                totalTabs: totalTabs
            };
        } else {
            console.log("No valid tabs to bookmark.");
            return {
                status: "No valid tabs",
                message: "No valid tabs found to bookmark.",
                newBookmarks: 0,
                duplicates: 0,
                totalTabs: totalTabs
            };
        }
    }

    // 3. Group the bookmarks using WebLLM and fallback classification
    const groupedBookmarks: { [category: string]: SmartBookmark[] } = {};
    const categories = ["Programming", "Travel", "Shopping", "News", "Research", "Social Media", "Entertainment", "Productivity", "Finance", "Health", "Miscellaneous"];

    // Separate bookmarks into those suitable for LLM and those for fallback
    const bookmarksForLLM: { bookmark: SmartBookmark, originalIndex: number }[] = [];
    const bookmarksForFallback: SmartBookmark[] = [];

    bookmarksToGroup.forEach((bookmark, index) => {
        // Only send to LLM if we have good title and/or description
        if (bookmark.title && bookmark.title.trim() !== '' &&
            (bookmark.description && bookmark.description.trim() !== '')) {
            bookmarksForLLM.push({ bookmark, originalIndex: index });
        } else {
            bookmarksForFallback.push(bookmark);
        }
    });

    console.log(`Splitting bookmarks: ${bookmarksForLLM.length} for LLM, ${bookmarksForFallback.length} for fallback`);

    // Track which bookmarks have been classified
    const classifiedIndices = new Set<number>();

    // Process bookmarks with LLM (if any)
    if (bookmarksForLLM.length > 0) {
        const promptContent = bookmarksForLLM.map((item, index) =>
            `${index}: "${item.bookmark.title}" - ${item.bookmark.description}`
        ).join('\n');

        const prompt = `Classify each webpage into ONE category based on its content and purpose.

Available categories: ${categories.join(', ')}.

Classification guidelines:
- Amazon, eBay, shopping sites → Shopping
- GitHub, Stack Overflow, coding sites → Programming
- News websites, articles → News
- Social media platforms → Social Media
- Productivity tools, work apps → Productivity

Pages to classify:
${promptContent}

Respond with only a JSON array like: [{"item": 0, "category": "Shopping"}, {"item": 1, "category": "Programming"}]`;

        console.log("🤖 === LLM CLASSIFICATION DEBUG ===");
        console.log(`📊 Processing ${bookmarksForLLM.length} bookmarks with LLM`);
        console.log("📝 FULL PROMPT SENT TO LLM:");
        console.log("=" .repeat(80));
        console.log(prompt);
        console.log("=" .repeat(80));

        // Log individual bookmarks being sent
        console.log("📋 BOOKMARKS BEING CLASSIFIED:");
        bookmarksForLLM.forEach((item, index) => {
            console.log(`  ${index}: "${item.bookmark.title}"`);
            console.log(`      URL: ${item.bookmark.url}`);
            console.log(`      Description: ${item.bookmark.description}`);
            console.log(`      Domain: ${extractDomainName(item.bookmark.url)}`);
            console.log("      ---");
        });

        let aiResponse = ""; // Declare outside try block for error logging
        try {
            const response = await llmEngine.chat.completions.create({
                messages: [{ role: "user", content: prompt }],
                temperature: 0.1,
                max_tokens: 1000, // Increased token limit
            });

            aiResponse = response.choices[0]?.message?.content || "";
            console.log("🤖 RAW LLM RESPONSE:");
            console.log("=" .repeat(80));
            console.log(aiResponse);
            console.log("=" .repeat(80));

            // Clean and parse the response
            const jsonString = aiResponse.replace(/```json\n?|\n?```/g, '').trim();
            console.log("🧹 CLEANED JSON STRING:");
            console.log(jsonString);

            const classifications = JSON.parse(jsonString);
            console.log("📊 PARSED CLASSIFICATIONS:");
            console.log(classifications);
            console.log(`✅ AI returned ${classifications.length} classifications for ${bookmarksForLLM.length} sent bookmarks`);

            // Process AI classifications
            let aiClassifiedCount = 0;
            console.log("🏷️  PROCESSING AI CLASSIFICATIONS:");
            classifications.forEach((item: { item: number, category: string }) => {
                if (item.item >= 0 && item.item < bookmarksForLLM.length) {
                    const originalCategory = item.category;
                    const category = categories.includes(item.category) ? item.category : "Miscellaneous";
                    const bookmark = bookmarksForLLM[item.item].bookmark;
                    const originalIndex = bookmarksForLLM[item.item].originalIndex;

                    // Log each classification decision
                    console.log(`  📌 Item ${item.item}: "${bookmark.title}"`);
                    console.log(`      URL: ${bookmark.url}`);
                    console.log(`      Domain: ${extractDomainName(bookmark.url)}`);
                    console.log(`      AI suggested: "${originalCategory}"`);
                    console.log(`      Final category: "${category}" ${originalCategory !== category ? '(corrected to valid category)' : ''}`);

                    // Check if this looks like a misclassification
                    const domain = extractDomainName(bookmark.url).toLowerCase();
                    if (domain.includes('amazon') && category !== 'Shopping') {
                        console.warn(`      ⚠️  POTENTIAL MISCLASSIFICATION: Amazon URL classified as "${category}" instead of "Shopping"`);
                    }
                    if (domain.includes('github') && category !== 'Programming') {
                        console.warn(`      ⚠️  POTENTIAL MISCLASSIFICATION: GitHub URL classified as "${category}" instead of "Programming"`);
                    }
                    if (domain.includes('news') && category !== 'News') {
                        console.warn(`      ⚠️  POTENTIAL MISCLASSIFICATION: News URL classified as "${category}" instead of "News"`);
                    }
                    console.log("      ---");

                    if (!groupedBookmarks[category]) {
                        groupedBookmarks[category] = [];
                    }
                    groupedBookmarks[category].push(bookmark);
                    classifiedIndices.add(originalIndex);
                    aiClassifiedCount++;
                } else {
                    console.warn(`❌ Invalid item index ${item.item} in AI response`);
                }
            });

            console.log(`✅ Successfully classified ${aiClassifiedCount} bookmarks using AI`);

            // Find bookmarks that LLM didn't classify
            const unclassifiedByLLM: SmartBookmark[] = [];
            bookmarksForLLM.forEach((item, index) => {
                if (!classifications.some((cls: any) => cls.item === index)) {
                    unclassifiedByLLM.push(item.bookmark);
                    console.log(`LLM missed bookmark: ${item.bookmark.title}`);
                }
            });

            if (unclassifiedByLLM.length > 0) {
                console.log(`LLM missed ${unclassifiedByLLM.length} bookmarks, adding to fallback`);
                bookmarksForFallback.push(...unclassifiedByLLM);
            }

        } catch (e) {
            console.error("❌ === LLM PARSING ERROR ===");
            console.error("Failed to parse WebLLM response, moving all LLM bookmarks to fallback.");
            console.error("Error details:", e);
            console.error("Raw AI response that failed to parse:", aiResponse);
            console.error("=" .repeat(80));
            bookmarksForFallback.push(...bookmarksForLLM.map(item => item.bookmark));
        }
    }

    // Process remaining bookmarks with fallback classification
    if (bookmarksForFallback.length > 0) {
        console.log("🔄 === FALLBACK CLASSIFICATION DEBUG ===");
        console.log(`📊 Using fallback classification for ${bookmarksForFallback.length} bookmarks`);

        let fallbackCount = 0;
        for (const bookmark of bookmarksForFallback) {
            const category = classifyByDomainAndKeywords(bookmark.url, bookmark.title, bookmark.description);
            console.log(`  📌 Fallback: "${bookmark.title}"`);
            console.log(`      URL: ${bookmark.url}`);
            console.log(`      Domain: ${extractDomainName(bookmark.url)}`);
            console.log(`      Classified as: "${category}"`);
            console.log("      ---");

            if (!groupedBookmarks[category]) {
                groupedBookmarks[category] = [];
            }
            groupedBookmarks[category].push(bookmark);
            fallbackCount++;
        }

        console.log(`✅ Fallback classification completed for ${fallbackCount} bookmarks`);
    }

    // 4. Save the grouped bookmarks to local storage
    const sessionKey = `bookmarks_${Date.now()}`;

    // Count total bookmarks in grouped structure
    let totalGroupedBookmarks = 0;
    for (const categoryBookmarks of Object.values(groupedBookmarks)) {
        totalGroupedBookmarks += categoryBookmarks.length;
    }

    console.log("📊 === FINAL CLASSIFICATION SUMMARY ===");
    console.log(`📋 Total bookmarks in groups: ${totalGroupedBookmarks}`);
    console.log(`📋 Original bookmarks to process: ${bookmarksToGroup.length}`);
    console.log("📁 FINAL CATEGORIES AND COUNTS:");
    Object.entries(groupedBookmarks).forEach(([category, bookmarks]) => {
        console.log(`  📁 ${category}: ${bookmarks.length} bookmarks`);
        bookmarks.forEach(bookmark => {
            console.log(`    - "${bookmark.title}" (${extractDomainName(bookmark.url)})`);
        });
    });
    console.log("=" .repeat(80));

    await chrome.storage.local.set({ [sessionKey]: groupedBookmarks });

    // 5. Update the URL index for faster future lookups
    await updateUrlIndex(bookmarksToGroup.map(b => b.url));

    const totalTabs = tabs.length;
    const newBookmarks = bookmarksToGroup.length;

    console.log(`✅ Bookmarks saved successfully! New: ${newBookmarks}, Duplicates: ${duplicateCount}, Total tabs: ${totalTabs}`);

    return {
        status: "Processing complete",
        message: `Saved ${newBookmarks} new bookmarks. Skipped ${duplicateCount} duplicates out of ${totalTabs} total tabs.`,
        newBookmarks: newBookmarks,
        duplicates: duplicateCount,
        totalTabs: totalTabs
    };

    } catch (error) {
        console.error("Error in processAndSaveTabs:", error);
        return {
            status: "Error",
            message: `Failed to process tabs: ${error instanceof Error ? error.message : 'Unknown error'}`,
            newBookmarks: 0,
            duplicates: 0,
            totalTabs: 0
        };
    }
}

// Extract domain name from URL for fallback title
function extractDomainName(url: string): string {
    try {
        const domain = new URL(url).hostname;
        return domain.replace('www.', '');
    } catch (e) {
        return url;
    }
}

// Enhanced fallback classification using domain and keywords
function classifyByDomainAndKeywords(url: string, title: string, description: string): string {
    const domain = extractDomainName(url).toLowerCase();
    const text = (title + " " + description + " " + domain).toLowerCase();
    
    // Domain-based classification
    if (domain.includes('github') || domain.includes('stackoverflow') || domain.includes('codepen')) return "Programming";
    if (domain.includes('booking') || domain.includes('expedia') || domain.includes('airbnb')) return "Travel";
    if (domain.includes('amazon') || domain.includes('ebay') || domain.includes('shop')) return "Shopping";
    if (domain.includes('news') || domain.includes('cnn') || domain.includes('bbc')) return "News";
    if (domain.includes('facebook') || domain.includes('twitter') || domain.includes('instagram') || domain.includes('linkedin')) return "Social Media";
    if (domain.includes('youtube') || domain.includes('netflix') || domain.includes('twitch')) return "Entertainment";
    if (domain.includes('bank') || domain.includes('paypal') || domain.includes('finance')) return "Finance";
    
    // Keyword-based classification (fallback)
    if (text.includes('github') || text.includes('code') || text.includes('programming') || text.includes('developer')) return "Programming";
    if (text.includes('travel') || text.includes('hotel') || text.includes('flight')) return "Travel";
    if (text.includes('shop') || text.includes('buy') || text.includes('cart') || text.includes('price')) return "Shopping";
    if (text.includes('news') || text.includes('breaking') || text.includes('article')) return "News";
    if (text.includes('research') || text.includes('study') || text.includes('academic')) return "Research";
    if (text.includes('social') || text.includes('facebook') || text.includes('twitter') || text.includes('instagram')) return "Social Media";
    if (text.includes('video') || text.includes('movie') || text.includes('entertainment') || text.includes('youtube')) return "Entertainment";
    if (text.includes('productivity') || text.includes('tool') || text.includes('work')) return "Productivity";
    if (text.includes('finance') || text.includes('bank') || text.includes('money') || text.includes('investment')) return "Finance";
    if (text.includes('health') || text.includes('medical') || text.includes('fitness')) return "Health";
    
    return "Miscellaneous";
}

// Function to retrieve all saved bookmarks from storage
async function getAllBookmarks(): Promise<{ [sessionKey: string]: { [category: string]: SmartBookmark[] } }> {
    try {
        // Get all stored data from chrome.storage.local
        const result = await chrome.storage.local.get(null);

        console.log('Getting all bookmarks ****');
        console.log(result);

        // Filter for bookmark sessions (keys starting with 'bookmarks_')
        const bookmarkSessions: { [sessionKey: string]: { [category: string]: SmartBookmark[] } } = {};
        for (const [key, value] of Object.entries(result)) {
            if (key.startsWith('bookmarks_') && typeof value === 'object') {
                bookmarkSessions[key] = value as { [category: string]: SmartBookmark[] };
            }
        }

        return bookmarkSessions;
    } catch (error) {
        console.error("Error retrieving bookmarks from storage:", error);
        throw error;
    }
}

// Optimized function to get existing URLs as a Set for fast duplicate checking
async function getExistingUrlsSet(): Promise<Set<string>> {
    try {
        console.log("Getting existing URLs for deduplication...");

        // First try to get the URL index if it exists
        const indexResult = await chrome.storage.local.get(['url_index']);
        if (indexResult.url_index && Array.isArray(indexResult.url_index)) {
            console.log(`Using existing URL index with ${indexResult.url_index.length} URLs`);
            return new Set(indexResult.url_index);
        }

        // Fallback: build the set from all bookmarks and create the index
        console.log("URL index not found, building from existing bookmarks...");
        const existingBookmarks = await getAllBookmarks();
        const urlSet = new Set<string>();

        // Check if we have any bookmarks at all
        const sessionCount = Object.keys(existingBookmarks).length;
        console.log(`Found ${sessionCount} bookmark sessions`);

        if (sessionCount === 0) {
            console.log("No existing bookmarks found, returning empty set");
            return urlSet;
        }

        for (const session of Object.values(existingBookmarks)) {
            for (const categoryBookmarks of Object.values(session)) {
                for (const bookmark of categoryBookmarks) {
                    if (bookmark && bookmark.url) {
                        urlSet.add(bookmark.url);
                    }
                }
            }
        }

        // Save the index for future use (only if we have URLs)
        if (urlSet.size > 0) {
            await chrome.storage.local.set({ url_index: Array.from(urlSet) });
            console.log(`Built and saved URL index with ${urlSet.size} URLs`);
        }

        return urlSet;
    } catch (error) {
        console.error("Error getting existing URLs:", error);
        return new Set<string>();
    }
}

// Function to update the URL index with new URLs
async function updateUrlIndex(newUrls: string[]): Promise<void> {
    try {
        const indexResult = await chrome.storage.local.get(['url_index']);
        const existingIndex = indexResult.url_index || [];
        const urlSet = new Set([...existingIndex, ...newUrls]);

        await chrome.storage.local.set({ url_index: Array.from(urlSet) });
        console.log(`Updated URL index, now contains ${urlSet.size} URLs`);
    } catch (error) {
        console.error("Error updating URL index:", error);
    }
}

// Function to reset all bookmarks (background script version)
async function resetAllBookmarks(): Promise<{ status: string; message: string; }> {
    try {
        // Get all storage keys
        const allData = await chrome.storage.local.get(null);
        const keysToRemove: string[] = [];

        // Find all bookmark-related keys
        for (const key of Object.keys(allData)) {
            if (key.startsWith('bookmarks_') || key === 'url_index') {
                keysToRemove.push(key);
            }
        }

        // Remove all bookmark data
        if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
            console.log(`Reset complete: Removed ${keysToRemove.length} bookmark-related storage keys`);

            return {
                status: "Reset complete",
                message: `Successfully removed ${keysToRemove.length} bookmark sessions and URL index.`
            };
        } else {
            console.log("No bookmarks found to reset");
            return {
                status: "No bookmarks found",
                message: "No bookmarks were found to reset."
            };
        }

    } catch (error) {
        console.error("Error resetting bookmarks:", error);
        throw error;
    }
}

// Helper function to inject a script and get the meta description
async function getMetaDescription(tabId: number): Promise<string> {
    try {
        const results = await chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: () => {
                const metaDesc = document.querySelector('meta[name="description"]')?.getAttribute('content');
                const ogDesc = document.querySelector('meta[property="og:description"]')?.getAttribute('content');
                return metaDesc || ogDesc || '';
            }
        });
        return (results && results[0] && results[0].result) || '';
    } catch (e) {
        console.warn(`Could not access content script on tab ${tabId}:`, e);
        return '';
    }
}

// Helper function to get the visit count from history
async function getVisitCount(url: string): Promise<number> {
    try {
        const historyItems = await chrome.history.getVisits({ url: url });
        return historyItems.length;
    } catch (e) {
        console.warn(`Could not get history for ${url}:`, e);
        return 0;
    }
}
