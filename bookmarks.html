<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BookSmart - Saved Bookmarks</title>
    <style>
        :root {
            /* Light theme colors */
            --bg-primary: #f5f5f5;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f8f9fa;
            --bg-quaternary: #e9ecef;
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-tertiary: #999999;
            --border-primary: #e0e0e0;
            --border-secondary: #f0f0f0;
            --accent-primary: #007BFF;
            --accent-secondary: #00BFFF;
            --accent-success: #28a745;
            --accent-success-secondary: #20c997;
            --accent-danger: #dc3545;
            --accent-warning: #ffc107;
            --accent-info: #17a2b8;
            --accent-gray: #6c757d;
            --shadow-light: rgba(0,0,0,0.1);
            --shadow-medium: rgba(0,0,0,0.15);
            --gradient-primary: linear-gradient(135deg, #007BFF, #00BFFF);
            --gradient-success: linear-gradient(45deg, #28a745, #20c997);
            --gradient-header: linear-gradient(135deg, #f8f9fa, #e9ecef);
            --gradient-header-dark: linear-gradient(135deg, #2c2c2c, #404040);
        }

        [data-theme="dark"] {
            /* Dark theme colors */
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --bg-tertiary: #3a3a3a;
            --bg-quaternary: #4a4a4a;
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --text-tertiary: #999999;
            --border-primary: #404040;
            --border-secondary: #505050;
            --accent-primary: #4dabf7;
            --accent-secondary: #74c0fc;
            --accent-success: #51cf66;
            --accent-success-secondary: #69db7c;
            --accent-danger: #ff6b6b;
            --accent-warning: #ffd43b;
            --accent-info: #22b8cf;
            --accent-gray: #868e96;
            --shadow-light: rgba(0,0,0,0.3);
            --shadow-medium: rgba(0,0,0,0.4);
            --gradient-primary: linear-gradient(135deg, #4dabf7, #74c0fc);
            --gradient-success: linear-gradient(45deg, #51cf66, #69db7c);
            --gradient-header: linear-gradient(135deg, #3a3a3a, #4a4a4a);
            --gradient-header-dark: linear-gradient(135deg, #2c2c2c, #404040);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .header {
            background: var(--gradient-primary);
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 2px 10px var(--shadow-light);
            position: relative;
        }

        [data-theme="dark"] .header {
            background: var(--gradient-header-dark);
            color: var(--text-primary);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .header-settings {
            position: absolute;
            top: 1rem;
            right: 2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .settings-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-right: 0.5rem;
        }

        .theme-toggle {
            display: flex;
            gap: 0.25rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.25rem;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .theme-btn {
            padding: 0.5rem 0.75rem;
            border: none;
            background: transparent;
            color: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
            font-weight: 500;
            opacity: 0.7;
        }

        .theme-btn:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.1);
        }

        .theme-btn.active {
            background: rgba(255, 255, 255, 0.2);
            opacity: 1;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] .theme-btn {
            color: var(--text-primary);
        }

        [data-theme="dark"] .theme-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .theme-btn.active {
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .controls {
            background: var(--bg-secondary);
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px var(--shadow-light);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .view-mode-toggle {
            display: flex;
            gap: 0.5rem;
            background: var(--bg-tertiary);
            padding: 0.25rem;
            border-radius: 8px;
            border: 2px solid var(--border-primary);
        }

        .view-mode-btn {
            padding: 0.5rem 1rem;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .view-mode-btn:hover {
            background: rgba(var(--accent-primary-rgb, 0, 123, 255), 0.1);
            color: var(--accent-primary);
        }

        .view-mode-btn.active {
            background: var(--accent-primary);
            color: white;
            box-shadow: 0 2px 4px rgba(var(--accent-primary-rgb, 0, 123, 255), 0.3);
        }

        .search-box {
            flex: 1;
            min-width: 300px;
        }

        .search-box input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-primary);
            border-radius: 8px;
            font-size: 1rem;
            background: var(--bg-secondary);
            color: var(--text-primary);
            transition: border-color 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--accent-primary);
        }

        .search-box input::placeholder {
            color: var(--text-tertiary);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid var(--accent-primary);
            background: var(--bg-secondary);
            color: var(--accent-primary);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: var(--accent-primary);
            color: white;
        }

        .export-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 0.5rem 1rem;
            border: 2px solid var(--accent-success);
            background: var(--bg-secondary);
            color: var(--accent-success);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .export-btn:hover {
            background: var(--accent-success);
            color: white;
        }

        .export-btn.secondary {
            border-color: var(--accent-gray);
            color: var(--accent-gray);
        }

        .export-btn.secondary:hover {
            background: var(--accent-gray);
            color: white;
        }

        .reset-btn {
            padding: 0.5rem 1rem;
            border: 2px solid var(--accent-danger);
            background: var(--bg-secondary);
            color: var(--accent-danger);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .reset-btn:hover {
            background: var(--accent-danger);
            color: white;
        }

        .reset-section {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-primary);
            display: flex;
            justify-content: center;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            font-size: 1.2rem;
            color: var(--text-secondary);
        }

        .no-bookmarks {
            text-align: center;
            padding: 3rem;
            background: var(--bg-secondary);
            border-radius: 8px;
            box-shadow: 0 2px 10px var(--shadow-light);
        }

        .no-bookmarks h2 {
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        .no-bookmarks p {
            color: var(--text-tertiary);
        }

        .bookmark-group {
            background: var(--bg-secondary);
            border-radius: 8px;
            box-shadow: 0 2px 10px var(--shadow-light);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .group-header {
            background: var(--gradient-header);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-primary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .group-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .group-count {
            background: var(--accent-primary);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .bookmark-list {
            padding: 0;
        }

        .bookmark-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-secondary);
            transition: background-color 0.2s ease;
        }

        .bookmark-item:last-child {
            border-bottom: none;
        }

        .bookmark-item:hover {
            background-color: var(--bg-tertiary);
        }

        .bookmark-favicon {
            width: 24px;
            height: 24px;
            margin-right: 1rem;
            border-radius: 4px;
            flex-shrink: 0;
        }

        .bookmark-favicon.default {
            background: var(--accent-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .bookmark-content {
            flex: 1;
            min-width: 0;
        }

        .bookmark-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
            cursor: pointer;
            text-decoration: none;
            display: block;
        }

        .bookmark-title:hover {
            color: var(--accent-primary);
        }

        .bookmark-url {
            font-size: 0.9rem;
            color: var(--text-secondary);
            word-break: break-all;
            margin-bottom: 0.25rem;
        }

        .bookmark-description {
            font-size: 0.85rem;
            color: var(--text-tertiary);
            line-height: 1.4;
        }

        .bookmark-meta {
            display: flex;
            gap: 1rem;
            margin-top: 0.5rem;
            font-size: 0.8rem;
            color: var(--text-tertiary);
        }

        .bookmark-actions {
            display: flex;
            gap: 0.5rem;
            margin-left: 1rem;
        }

        .action-btn {
            padding: 0.5rem;
            border: none;
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.9rem;
        }

        .action-btn:hover {
            background: var(--accent-primary);
            color: white;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--accent-primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        /* Group Navigation Styles */
        .group-navigation {
            background: var(--bg-secondary);
            border-radius: 8px;
            box-shadow: 0 2px 10px var(--shadow-light);
            padding: 2rem;
        }

        .group-navigation-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .group-navigation-header h2 {
            font-size: 1.8rem;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .group-navigation-header p {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .visit-info {
            color: var(--accent-primary) !important;
            font-size: 0.9rem !important;
            font-style: italic;
            margin-top: 0.5rem;
        }

        .group-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .group-card {
            background: var(--gradient-header);
            border: 2px solid var(--border-primary);
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px var(--shadow-light);
            display: flex;
            flex-direction: column;
        }

        .group-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px var(--shadow-medium);
            border-color: var(--accent-primary);
        }

        .group-card-content {
            padding: 1.5rem;
            cursor: pointer;
            flex: 1;
        }

        .group-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .group-card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .group-card-count {
            background: var(--accent-primary);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .group-card-stats {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .group-stat {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
        }

        .group-stat .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .group-stat .stat-value {
            color: var(--text-primary);
            font-weight: 600;
        }

        .group-card-actions {
            display: flex;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            background: rgba(0, 123, 255, 0.05);
            border-top: 1px solid #e0e0e0;
        }

        .group-action-btn {
            flex: 1;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
        }

        .group-action-btn.primary {
            background: var(--accent-primary);
            color: white;
        }

        .group-action-btn.primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .group-action-btn.secondary {
            background: var(--accent-success);
            color: white;
        }

        .group-action-btn.secondary:hover {
            background: #1e7e34;
            transform: translateY(-1px);
        }

        /* Group Detail View Styles */
        .group-detail {
            background: var(--bg-secondary);
            border-radius: 8px;
            box-shadow: 0 2px 10px var(--shadow-light);
            overflow: hidden;
        }

        .group-detail-header {
            background: var(--gradient-header);
            padding: 2rem;
            border-bottom: 1px solid var(--border-primary);
        }

        .group-detail-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            gap: 1rem;
        }

        .back-btn {
            background: #007BFF;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #0056b3;
            transform: translateX(-2px);
        }

        .open-all-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .open-all-btn:hover {
            background: #1e7e34;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
        }

        .group-detail-info h2 {
            font-size: 2rem;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .group-detail-stats {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .detail-stat {
            color: #666;
            font-size: 0.95rem;
        }

        .detail-stat strong {
            color: #007BFF;
        }

        .group-detail-bookmarks {
            padding: 0;
        }

        .group-bookmark-item {
            border-bottom: 1px solid var(--border-secondary);
        }

        .group-bookmark-item:last-child {
            border-bottom: none;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e0e0e0;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 0.5rem;
            border-radius: 8px;
            transition: background-color 0.2s ease;
        }

        .close-btn:hover {
            background-color: #f0f0f0;
        }

        .json-viewer {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 400px;
            overflow-y: auto;
        }

        .copy-json-btn {
            margin-top: 1rem;
            padding: 0.75rem 1.5rem;
            background: #007BFF;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.2s ease;
        }

        .copy-json-btn:hover {
            background: #0056b3;
        }

        .confirm-modal {
            display: none;
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.6);
        }

        .confirm-content {
            background-color: white;
            margin: 15% auto;
            padding: 2rem;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            text-align: center;
            position: relative;
        }

        .confirm-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #dc3545;
            margin-bottom: 1rem;
        }

        .confirm-message {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }

        .confirm-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            color: #856404;
            font-size: 0.95rem;
        }

        .confirm-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .confirm-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .confirm-btn.danger {
            background: #dc3545;
            color: white;
        }

        .confirm-btn.danger:hover {
            background: #c82333;
        }

        .confirm-btn.cancel {
            background: #6c757d;
            color: white;
        }

        .confirm-btn.cancel:hover {
            background: #5a6268;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                min-width: auto;
            }

            .filter-buttons, .export-buttons {
                justify-content: center;
            }

            .bookmark-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .bookmark-actions {
                margin-left: 0;
                align-self: stretch;
                justify-content: center;
            }

            .modal-content {
                margin: 10% auto;
                width: 95%;
                padding: 1rem;
            }

            .view-mode-toggle {
                order: -1;
                width: 100%;
                justify-content: center;
            }

            .group-cards {
                grid-template-columns: 1fr;
            }

            .group-detail-stats {
                flex-direction: column;
                gap: 0.5rem;
            }

            .group-detail-header {
                padding: 1.5rem;
            }

            .group-detail-info h2 {
                font-size: 1.5rem;
            }

            .group-detail-nav {
                flex-direction: column;
                align-items: stretch;
                gap: 0.5rem;
            }

            .group-card-actions {
                flex-direction: column;
            }

            .group-action-btn {
                padding: 1rem;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-settings">
            <span class="settings-label">Theme:</span>
            <div class="theme-toggle">
                <button class="theme-btn" data-theme="light">☀️ Light</button>
                <button class="theme-btn active" data-theme="system">🖥️ System</button>
                <button class="theme-btn" data-theme="dark">🌙 Dark</button>
            </div>
        </div>
        <h1>📚 BookSmart</h1>
        <p>Your Smart Bookmarks Collection</p>
    </div>

    <div class="container">
        <div class="controls">
            <div class="view-mode-toggle">
                <button class="view-mode-btn active" data-mode="sessions">📅 View by Sessions</button>
                <button class="view-mode-btn" data-mode="groups">📁 View by Groups</button>
            </div>
            <div class="search-box">
                <input type="text" id="search-input" placeholder="Search bookmarks...">
            </div>
            <div class="filter-buttons">
                <button class="filter-btn active" data-category="all">All</button>
            </div>
            <div class="export-buttons">
                <button class="export-btn" id="view-json-btn">📄 View JSON</button>
                <button class="export-btn" id="export-json-btn">💾 Export JSON</button>
                <button class="export-btn secondary" id="export-chrome-btn">🌐 Export for Chrome</button>
                <button class="export-btn secondary" id="export-csv-btn">📊 Export CSV</button>
            </div>
            <div class="reset-section">
                <button class="reset-btn" id="reset-bookmarks-btn">🗑️ Reset All Bookmarks</button>
            </div>
        </div>

        <div class="stats" id="stats-container">
            <!-- Stats will be populated by JavaScript -->
        </div>

        <div id="loading" class="loading">
            Loading your bookmarks...
        </div>

        <div id="no-bookmarks" class="no-bookmarks" style="display: none;">
            <h2>No bookmarks found</h2>
            <p>Start by bookmarking some tabs using the extension popup!</p>
        </div>

        <div id="bookmarks-container">
            <!-- Bookmark groups will be populated by JavaScript -->
        </div>
    </div>

    <!-- JSON Viewer Modal -->
    <div id="json-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Raw JSON Data</h2>
                <button class="close-btn" id="close-modal">&times;</button>
            </div>
            <div class="json-viewer" id="json-content">
                <!-- JSON content will be populated by JavaScript -->
            </div>
            <button class="copy-json-btn" id="copy-json-btn">Copy to Clipboard</button>
        </div>
    </div>

    <!-- Reset Confirmation Modal -->
    <div id="confirm-modal" class="confirm-modal">
        <div class="confirm-content">
            <h2 class="confirm-title">⚠️ Reset All Bookmarks</h2>
            <p class="confirm-message">
                Are you sure you want to delete <strong>ALL</strong> your saved bookmarks?
            </p>
            <div class="confirm-warning">
                <strong>Warning:</strong> This action cannot be undone. All bookmark sessions, categories, and the URL index will be permanently deleted.
            </div>
            <div class="confirm-buttons">
                <button class="confirm-btn cancel" id="cancel-reset-btn">Cancel</button>
                <button class="confirm-btn danger" id="confirm-reset-btn">Yes, Delete All</button>
            </div>
        </div>
    </div>

    <script src="js/bookmarks.js"></script>
</body>
</html>
